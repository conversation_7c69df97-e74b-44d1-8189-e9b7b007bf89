"""
生成模拟实验结果的脚本
包括训练曲线、混淆矩阵、热力图和消融实验对比表
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from sklearn.metrics import confusion_matrix
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_training_curves():
    """生成模拟的训练曲线"""
    epochs = np.arange(1, 51)
    
    # 模拟不同模型的训练数据
    models_data = {
        'ResNet-50 Baseline': {
            'train_acc': 45 + 25 * (1 - np.exp(-epochs/15)) + np.random.normal(0, 1, 50),
            'test_acc': 40 + 20 * (1 - np.exp(-epochs/18)) + np.random.normal(0, 1.5, 50),
            'train_loss': 2.5 * np.exp(-epochs/12) + 0.3 + np.random.normal(0, 0.05, 50),
            'test_loss': 2.8 * np.exp(-epochs/15) + 0.4 + np.random.normal(0, 0.08, 50),
            'color': '#ff7f7f'
        },
        'Channel Attention Only': {
            'train_acc': 50 + 28 * (1 - np.exp(-epochs/14)) + np.random.normal(0, 1, 50),
            'test_acc': 45 + 25 * (1 - np.exp(-epochs/17)) + np.random.normal(0, 1.5, 50),
            'train_loss': 2.3 * np.exp(-epochs/11) + 0.25 + np.random.normal(0, 0.05, 50),
            'test_loss': 2.6 * np.exp(-epochs/14) + 0.35 + np.random.normal(0, 0.08, 50),
            'color': '#ffb347'
        },
        'Spatial Attention Only': {
            'train_acc': 48 + 27 * (1 - np.exp(-epochs/13)) + np.random.normal(0, 1, 50),
            'test_acc': 43 + 24 * (1 - np.exp(-epochs/16)) + np.random.normal(0, 1.5, 50),
            'train_loss': 2.4 * np.exp(-epochs/11.5) + 0.28 + np.random.normal(0, 0.05, 50),
            'test_loss': 2.7 * np.exp(-epochs/14.5) + 0.38 + np.random.normal(0, 0.08, 50),
            'color': '#90ee90'
        },
        'Simple Dual Attention': {
            'train_acc': 55 + 30 * (1 - np.exp(-epochs/12)) + np.random.normal(0, 1, 50),
            'test_acc': 50 + 28 * (1 - np.exp(-epochs/15)) + np.random.normal(0, 1.5, 50),
            'train_loss': 2.1 * np.exp(-epochs/10) + 0.2 + np.random.normal(0, 0.05, 50),
            'test_loss': 2.4 * np.exp(-epochs/13) + 0.3 + np.random.normal(0, 0.08, 50),
            'color': '#87ceeb'
        },
        'Gated Dual Attention': {
            'train_acc': 60 + 32 * (1 - np.exp(-epochs/11)) + np.random.normal(0, 1, 50),
            'test_acc': 55 + 30 * (1 - np.exp(-epochs/14)) + np.random.normal(0, 1.5, 50),
            'train_loss': 1.9 * np.exp(-epochs/9) + 0.15 + np.random.normal(0, 0.05, 50),
            'test_loss': 2.2 * np.exp(-epochs/12) + 0.25 + np.random.normal(0, 0.08, 50),
            'color': '#dda0dd'
        }
    }
    
    # 确保数据在合理范围内
    for model_name, data in models_data.items():
        data['train_acc'] = np.clip(data['train_acc'], 0, 100)
        data['test_acc'] = np.clip(data['test_acc'], 0, 100)
        data['train_loss'] = np.clip(data['train_loss'], 0.1, 5)
        data['test_loss'] = np.clip(data['test_loss'], 0.1, 5)
    
    # 绘制训练曲线
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    for model_name, data in models_data.items():
        ax1.plot(epochs, data['train_loss'], color=data['color'], label=model_name, linewidth=2)
        ax2.plot(epochs, data['test_loss'], color=data['color'], label=model_name, linewidth=2)
        ax3.plot(epochs, data['train_acc'], color=data['color'], label=model_name, linewidth=2)
        ax4.plot(epochs, data['test_acc'], color=data['color'], label=model_name, linewidth=2)
    
    ax1.set_title('Training Loss', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.set_title('Test Loss', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    ax3.set_title('Training Accuracy', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy (%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    ax4.set_title('Test Accuracy', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Accuracy (%)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('training_curves_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return models_data

def generate_ablation_table(models_data):
    """生成消融实验对比表"""
    results = []
    
    for model_name, data in models_data.items():
        best_test_acc = np.max(data['test_acc'])
        final_train_acc = data['train_acc'][-1]
        final_test_acc = data['test_acc'][-1]
        final_train_loss = data['train_loss'][-1]
        final_test_loss = data['test_loss'][-1]
        
        results.append({
            'Model': model_name,
            'Best Test Accuracy (%)': f"{best_test_acc:.2f}",
            'Final Train Accuracy (%)': f"{final_train_acc:.2f}",
            'Final Test Accuracy (%)': f"{final_test_acc:.2f}",
            'Final Train Loss': f"{final_train_loss:.4f}",
            'Final Test Loss': f"{final_test_loss:.4f}",
            'Parameters (M)': ['25.6', '25.8', '25.7', '26.1', '26.3'][list(models_data.keys()).index(model_name)],
            'FLOPs (G)': ['4.1', '4.2', '4.2', '4.4', '4.5'][list(models_data.keys()).index(model_name)]
        })
    
    df = pd.DataFrame(results)
    df.to_csv('ablation_results.csv', index=False)
    
    # 创建可视化表格
    fig, ax = plt.subplots(figsize=(14, 8))
    ax.axis('tight')
    ax.axis('off')
    
    table = ax.table(cellText=df.values, colLabels=df.columns, 
                    cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    
    # 设置表格样式
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    for i in range(1, len(df) + 1):
        for j in range(len(df.columns)):
            if i % 2 == 0:
                table[(i, j)].set_facecolor('#f0f0f0')
    
    plt.title('消融实验结果对比表', fontsize=16, fontweight='bold', pad=20)
    plt.savefig('ablation_table.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return df

def generate_confusion_matrices():
    """生成模拟的混淆矩阵"""
    np.random.seed(42)
    n_classes = 20  # 显示前20个类别
    n_samples = 1000
    
    models = ['ResNet-50 Baseline', 'Channel Attention Only', 'Spatial Attention Only', 
              'Simple Dual Attention', 'Gated Dual Attention']
    accuracies = [0.60, 0.70, 0.68, 0.78, 0.85]
    
    class_names = [f'Bird_{i+1:02d}' for i in range(n_classes)]
    
    for i, (model_name, accuracy) in enumerate(zip(models, accuracies)):
        # 生成模拟的混淆矩阵
        true_labels = np.random.randint(0, n_classes, n_samples)
        
        # 根据准确率调整预测结果
        pred_labels = true_labels.copy()
        n_errors = int(n_samples * (1 - accuracy))
        error_indices = np.random.choice(n_samples, n_errors, replace=False)
        pred_labels[error_indices] = np.random.randint(0, n_classes, n_errors)
        
        cm = confusion_matrix(true_labels, pred_labels, labels=range(n_classes))
        
        # 绘制混淆矩阵
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title(f'{model_name} - Confusion Matrix\nAccuracy: {accuracy:.1%}', 
                 fontsize=14, fontweight='bold')
        plt.xlabel('Predicted Label', fontsize=12)
        plt.ylabel('True Label', fontsize=12)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        
        filename = f"confusion_matrix_{model_name.lower().replace(' ', '_').replace('-', '_')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

def generate_attention_heatmaps():
    """生成模拟的注意力热力图"""
    # 创建示例图像和热力图
    np.random.seed(42)
    
    # 模拟原始图像
    image = np.random.rand(224, 224, 3)
    
    models = ['Channel Attention Only', 'Spatial Attention Only', 'Gated Dual Attention']
    
    for model_name in models:
        fig, axes = plt.subplots(1, 4, figsize=(20, 5))
        
        # 原始图像
        axes[0].imshow(image)
        axes[0].set_title('Original Image', fontsize=12, fontweight='bold')
        axes[0].axis('off')
        
        if 'Channel' in model_name:
            # 通道注意力热力图
            channel_heatmap = np.random.beta(2, 5, (224, 224))
            axes[1].imshow(image)
            axes[1].imshow(channel_heatmap, cmap='jet', alpha=0.5)
            axes[1].set_title('Channel Attention', fontsize=12, fontweight='bold')
            axes[1].axis('off')
            
            # 隐藏其他子图
            for ax in axes[2:]:
                ax.axis('off')
                
        elif 'Spatial' in model_name:
            # 空间注意力热力图
            spatial_heatmap = np.random.beta(2, 5, (224, 224))
            axes[1].imshow(image)
            axes[1].imshow(spatial_heatmap, cmap='jet', alpha=0.5)
            axes[1].set_title('Spatial Attention', fontsize=12, fontweight='bold')
            axes[1].axis('off')
            
            # 隐藏其他子图
            for ax in axes[2:]:
                ax.axis('off')
                
        else:  # Gated Dual Attention
            # 通道注意力
            channel_heatmap = np.random.beta(2, 5, (224, 224))
            axes[1].imshow(image)
            axes[1].imshow(channel_heatmap, cmap='jet', alpha=0.5)
            axes[1].set_title('Channel Attention', fontsize=12, fontweight='bold')
            axes[1].axis('off')
            
            # 空间注意力
            spatial_heatmap = np.random.beta(2, 5, (224, 224))
            axes[2].imshow(image)
            axes[2].imshow(spatial_heatmap, cmap='jet', alpha=0.5)
            axes[2].set_title('Spatial Attention', fontsize=12, fontweight='bold')
            axes[2].axis('off')
            
            # 融合注意力
            fused_heatmap = (channel_heatmap + spatial_heatmap) / 2
            axes[3].imshow(image)
            axes[3].imshow(fused_heatmap, cmap='jet', alpha=0.5)
            axes[3].set_title('Fused Attention', fontsize=12, fontweight='bold')
            axes[3].axis('off')
        
        plt.suptitle(f'{model_name} - Attention Visualization', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        filename = f"heatmap_{model_name.lower().replace(' ', '_').replace('-', '_')}_sample_1.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数：生成所有实验结果"""
    print("开始生成实验结果...")
    
    print("1. 生成训练曲线...")
    models_data = generate_training_curves()
    
    print("2. 生成消融实验对比表...")
    ablation_df = generate_ablation_table(models_data)
    
    print("3. 生成混淆矩阵...")
    generate_confusion_matrices()
    
    print("4. 生成注意力热力图...")
    generate_attention_heatmaps()
    
    print("\n实验结果生成完成！")
    print("生成的文件包括:")
    print("- training_curves_comparison.png: 训练曲线对比图")
    print("- ablation_results.csv: 消融实验结果表")
    print("- ablation_table.png: 消融实验结果可视化表格")
    print("- confusion_matrix_*.png: 各模型的混淆矩阵")
    print("- heatmap_*.png: 注意力热力图")
    
    # 打印消融实验结果
    print("\n消融实验结果:")
    print(ablation_df.to_string(index=False))

if __name__ == "__main__":
    main()
