"""
测试所有5个模型是否能正常工作的脚本
"""

import torch
import torch.nn as nn
from merged_train import (
    BaselineResNet, ChannelOnlyResNet, SpatialOnlyResNet, 
    SimpleAttentionFusionResNet, AttentionFusionResNet
)

def test_model_forward(model, model_name):
    """测试模型前向传播"""
    print(f"测试 {model_name}...")
    
    # 创建随机输入
    batch_size = 2
    input_tensor = torch.randn(batch_size, 3, 224, 224)
    
    try:
        # 设置为评估模式
        model.eval()
        
        # 前向传播
        with torch.no_grad():
            output = model(input_tensor)
        
        # 检查输出形状
        expected_shape = (batch_size, 200)  # CUB-200-2011有200个类别
        if output.shape == expected_shape:
            print(f"  ✓ {model_name} 前向传播成功")
            print(f"    输入形状: {input_tensor.shape}")
            print(f"    输出形状: {output.shape}")
            
            # 计算参数量
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            print(f"    总参数量: {total_params:,}")
            print(f"    可训练参数: {trainable_params:,}")
            
            return True
        else:
            print(f"  ✗ {model_name} 输出形状错误: 期望 {expected_shape}, 实际 {output.shape}")
            return False
            
    except Exception as e:
        print(f"  ✗ {model_name} 前向传播失败: {e}")
        return False

def compare_model_complexity():
    """比较各模型的复杂度"""
    models = [
        ("ResNet-50 Baseline", BaselineResNet()),
        ("Channel Attention Only", ChannelOnlyResNet()),
        ("Spatial Attention Only", SpatialOnlyResNet()),
        ("Simple Dual Attention", SimpleAttentionFusionResNet()),
        ("Gated Dual Attention", AttentionFusionResNet())
    ]
    
    print("\n" + "=" * 80)
    print("模型复杂度对比")
    print("=" * 80)
    print(f"{'Model Name':<25} {'Total Params':<15} {'Trainable Params':<18} {'Param Increase':<15}")
    print("-" * 80)
    
    baseline_params = None
    
    for model_name, model in models:
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        if baseline_params is None:
            baseline_params = total_params
            increase = "Baseline"
        else:
            increase_ratio = ((total_params - baseline_params) / baseline_params) * 100
            increase = f"+{increase_ratio:.1f}%"
        
        print(f"{model_name:<25} {total_params:>12,} {trainable_params:>15,} {increase:>12}")

def test_attention_mechanisms():
    """测试注意力机制的输出"""
    print("\n" + "=" * 60)
    print("测试注意力机制")
    print("=" * 60)
    
    # 创建测试输入
    batch_size = 1
    feature_maps = torch.randn(batch_size, 2048, 7, 7)  # ResNet-50最后一层特征图
    
    # 测试通道注意力
    from merged_train import ChannelAttention
    channel_attn = ChannelAttention(2048)
    
    try:
        ca_output = channel_attn(feature_maps)
        print(f"✓ 通道注意力测试成功")
        print(f"  输入形状: {feature_maps.shape}")
        print(f"  输出形状: {ca_output.shape}")
        print(f"  输出范围: [{ca_output.min():.4f}, {ca_output.max():.4f}]")
    except Exception as e:
        print(f"✗ 通道注意力测试失败: {e}")
    
    # 测试空间注意力
    from merged_train import SpatialAttention
    spatial_attn = SpatialAttention()
    
    try:
        sa_output = spatial_attn(feature_maps)
        print(f"✓ 空间注意力测试成功")
        print(f"  输入形状: {feature_maps.shape}")
        print(f"  输出形状: {sa_output.shape}")
        print(f"  输出范围: [{sa_output.min():.4f}, {sa_output.max():.4f}]")
    except Exception as e:
        print(f"✗ 空间注意力测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试所有模型...")
    print("=" * 60)
    
    # 定义所有模型
    models = [
        ("ResNet-50 Baseline", BaselineResNet()),
        ("Channel Attention Only", ChannelOnlyResNet()),
        ("Spatial Attention Only", SpatialOnlyResNet()),
        ("Simple Dual Attention", SimpleAttentionFusionResNet()),
        ("Gated Dual Attention", AttentionFusionResNet())
    ]
    
    # 测试每个模型
    success_count = 0
    for model_name, model in models:
        if test_model_forward(model, model_name):
            success_count += 1
        print()
    
    # 测试结果汇总
    print("=" * 60)
    print(f"测试完成: {success_count}/{len(models)} 个模型通过测试")
    
    if success_count == len(models):
        print("✓ 所有模型都能正常工作！")
        
        # 比较模型复杂度
        compare_model_complexity()
        
        # 测试注意力机制
        test_attention_mechanisms()
        
        print("\n" + "=" * 60)
        print("所有测试完成！模型已准备好进行训练。")
        print("运行 'python merged_train.py' 开始训练")
        print("=" * 60)
        
    else:
        print("✗ 部分模型存在问题，请检查代码")

if __name__ == "__main__":
    main()
