# 消融实验对比分析

## 实验设计

```mermaid

graph LR
    subgraph "Baseline Models"
        A1[ResNet-50 Baseline<br/>No Attention]
        A2[Channel Attention Only<br/>SE-like Module]
        A3[Spatial Attention Only<br/>CBAM-like Module]
    end
    
    subgraph "Fusion Strategies"
        B1[Simple Addition<br/>CA + SA / 2]
        B2[Gated Fusion<br/>CA × Gate + SA × (1-Gate)]
    end
    
    subgraph "Performance Comparison"
        C1[Accuracy Results<br/>Baseline < Single < Simple < Gated]
        C2[Feature Quality<br/>More Discriminative Features]
        C3[Attention Visualization<br/>Better Localization]
    end
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    B1 --> C1
    B2 --> C1
    
    B2 --> C2
    B2 --> C3
    
    style A1 fill:#ffcdd2
    style A2 fill:#fff3e0
    style A3 fill:#e8f5e8
    style B1 fill:#e1f5fe
    style B2 fill:#f3e5f5
    style C1 fill:#c8e6c9
    style C2 fill:#c8e6c9
    style C3 fill:#c8e6c9

```

## 实验组设置

### 1. 基线模型
- **ResNet-50 Baseline**: 标准ResNet-50，无注意力机制
- **Channel Attention Only**: 仅使用通道注意力（类似SE-Net）
- **Spatial Attention Only**: 仅使用空间注意力（类似CBAM）

### 2. 融合策略对比
- **Simple Addition**: 简单平均融合 (CA + SA) / 2
- **Gated Fusion**: 门控自适应融合 CA × Gate + SA × (1-Gate)

### 3. 评估指标
- **准确率**: Top-1分类准确率
- **特征质量**: 通过可视化评估特征判别性
- **注意力定位**: 热力图分析注意力区域合理性
