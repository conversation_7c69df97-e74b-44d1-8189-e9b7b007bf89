# 双注意力融合ResNet架构图

## 完整架构流程

```mermaid

graph TD
    A[Input Image<br/>224×224×3] --> B[ResNet-50 Backbone]
    B --> C[Feature Maps<br/>7×7×2048]
    
    C --> D[Channel Attention Module]
    C --> E[Spatial Attention Module]
    
    D --> F[Global Average Pooling<br/>7×7×2048 → 1×1×2048]
    F --> G[FC Layer<br/>2048 → 128]
    G --> H[ReLU Activation]
    H --> I[FC Layer<br/>128 → 2048]
    I --> J[Sigmoid Activation]
    J --> K[Channel Weights<br/>1×1×2048]
    
    E --> L[Channel-wise<br/>Max & Avg Pool<br/>7×7×2048 → 7×7×2]
    L --> M[Concatenation<br/>7×7×2]
    M --> N[Conv2d<br/>2→1, kernel=7×7]
    N --> O[Sigmoid Activation]
    O --> P[Spatial Weights<br/>7×7×1]
    
    C --> Q[Element-wise Multiply]
    K --> Q
    C --> R[Element-wise Multiply]
    P --> R
    
    Q --> S[Channel Attended Features<br/>7×7×2048]
    R --> T[Spatial Attended Features<br/>7×7×2048]
    
    S --> U[Feature Concatenation<br/>7×7×4096]
    T --> U
    U --> V[Gate Network<br/>Conv2d 4096→2048]
    V --> W[Sigmoid Gate Weights<br/>7×7×2048]
    
    S --> X[Weighted Fusion<br/>CA × Gate + SA × (1-Gate)]
    T --> X
    W --> X
    
    X --> Y[Fused Features<br/>7×7×2048]
    Y --> Z[Global Average Pooling<br/>7×7×2048 → 1×1×2048]
    Z --> AA[Flatten<br/>2048]
    AA --> BB[Dropout 0.5]
    BB --> CC[FC Layer<br/>2048 → 200]
    CC --> DD[Output Predictions<br/>200 classes]
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style DD fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style D fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style E fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style V fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style X fill:#ffebee,stroke:#c62828,stroke-width:2px
    style C fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style Y fill:#f1f8e9,stroke:#558b2f,stroke-width:2px

```

## 架构说明

### 1. 输入处理
- 输入图像尺寸：224×224×3
- 经过ResNet-50骨干网络提取特征
- 得到7×7×2048的特征图

### 2. 双注意力机制
#### 通道注意力（Channel Attention）
- 全局平均池化压缩空间维度
- 两层全连接网络学习通道重要性
- 输出通道权重用于特征加权

#### 空间注意力（Spatial Attention）
- 通道维度的最大池化和平均池化
- 7×7卷积学习空间位置重要性
- 输出空间权重用于特征加权

### 3. 门控融合机制
- 将通道注意力和空间注意力特征拼接
- 门控网络学习自适应融合权重
- 动态平衡两种注意力的贡献

### 4. 分类输出
- 全局平均池化得到特征向量
- Dropout防止过拟合
- 全连接层输出200类鸟类预测
