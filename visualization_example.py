"""
5个模型完整可视化示例
包括：精度图、混淆矩阵、热力图、注意力架构图、中文消融实验对比表
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from sklearn.metrics import confusion_matrix
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_training_accuracy_curves():
    """生成5个模型的训练精度曲线对比图"""
    epochs = np.arange(1, 51)
    
    # 模拟5个模型的训练数据（中文模型名）
    models_data = {
        'ResNet-50基线模型': {
            'train_acc': 45 + 25 * (1 - np.exp(-epochs/15)) + np.random.normal(0, 1, 50),
            'test_acc': 40 + 20 * (1 - np.exp(-epochs/18)) + np.random.normal(0, 1.5, 50),
            'color': '#ff7f7f'
        },
        '仅通道注意力模型': {
            'train_acc': 50 + 28 * (1 - np.exp(-epochs/14)) + np.random.normal(0, 1, 50),
            'test_acc': 45 + 25 * (1 - np.exp(-epochs/17)) + np.random.normal(0, 1.5, 50),
            'color': '#ffb347'
        },
        '仅空间注意力模型': {
            'train_acc': 48 + 27 * (1 - np.exp(-epochs/13)) + np.random.normal(0, 1, 50),
            'test_acc': 43 + 24 * (1 - np.exp(-epochs/16)) + np.random.normal(0, 1.5, 50),
            'color': '#90ee90'
        },
        '简单双注意力模型': {
            'train_acc': 55 + 30 * (1 - np.exp(-epochs/12)) + np.random.normal(0, 1, 50),
            'test_acc': 50 + 28 * (1 - np.exp(-epochs/15)) + np.random.normal(0, 1.5, 50),
            'color': '#87ceeb'
        },
        '门控双注意力模型': {
            'train_acc': 60 + 32 * (1 - np.exp(-epochs/11)) + np.random.normal(0, 1, 50),
            'test_acc': 55 + 30 * (1 - np.exp(-epochs/14)) + np.random.normal(0, 1.5, 50),
            'color': '#dda0dd'
        }
    }
    
    # 确保数据在合理范围内
    for model_name, data in models_data.items():
        data['train_acc'] = np.clip(data['train_acc'], 0, 100)
        data['test_acc'] = np.clip(data['test_acc'], 0, 100)
    
    # 绘制精度曲线
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    for model_name, data in models_data.items():
        ax1.plot(epochs, data['train_acc'], color=data['color'], label=model_name, linewidth=2.5)
        ax2.plot(epochs, data['test_acc'], color=data['color'], label=model_name, linewidth=2.5)
    
    ax1.set_title('训练准确率对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('训练轮数 (Epoch)', fontsize=12)
    ax1.set_ylabel('准确率 (%)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(40, 100)
    
    ax2.set_title('测试准确率对比', fontsize=16, fontweight='bold')
    ax2.set_xlabel('训练轮数 (Epoch)', fontsize=12)
    ax2.set_ylabel('准确率 (%)', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(35, 90)
    
    plt.tight_layout()
    plt.savefig('五模型精度对比图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 精度对比图已生成: 五模型精度对比图.png")
    return models_data

def generate_confusion_matrices():
    """生成5个模型的混淆矩阵"""
    np.random.seed(42)
    n_classes = 20  # 显示前20个类别
    n_samples = 1000
    
    models = {
        'ResNet-50基线模型': 0.60,
        '仅通道注意力模型': 0.70,
        '仅空间注意力模型': 0.68,
        '简单双注意力模型': 0.78,
        '门控双注意力模型': 0.85
    }
    
    class_names = [f'鸟类_{i+1:02d}' for i in range(n_classes)]
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, (model_name, accuracy) in enumerate(models.items()):
        # 生成模拟的混淆矩阵
        true_labels = np.random.randint(0, n_classes, n_samples)
        pred_labels = true_labels.copy()
        n_errors = int(n_samples * (1 - accuracy))
        error_indices = np.random.choice(n_samples, n_errors, replace=False)
        pred_labels[error_indices] = np.random.randint(0, n_classes, n_errors)
        
        cm = confusion_matrix(true_labels, pred_labels, labels=range(n_classes))
        
        # 绘制混淆矩阵
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i],
                   xticklabels=class_names, yticklabels=class_names)
        axes[i].set_title(f'{model_name}\n准确率: {accuracy:.1%}', fontsize=12, fontweight='bold')
        axes[i].set_xlabel('预测标签', fontsize=10)
        axes[i].set_ylabel('真实标签', fontsize=10)
        axes[i].tick_params(axis='both', which='major', labelsize=8)
    
    # 隐藏最后一个子图
    axes[5].axis('off')
    
    plt.suptitle('五模型混淆矩阵对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('五模型混淆矩阵对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 混淆矩阵已生成: 五模型混淆矩阵对比.png")

def generate_attention_heatmaps():
    """生成注意力热力图对比"""
    np.random.seed(42)
    
    # 模拟原始图像
    image = np.random.rand(224, 224, 3)
    
    models = ['仅通道注意力模型', '仅空间注意力模型', '门控双注意力模型']
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    
    for i, model_name in enumerate(models):
        # 原始图像
        axes[i, 0].imshow(image)
        axes[i, 0].set_title('原始图像', fontsize=12, fontweight='bold')
        axes[i, 0].axis('off')
        
        if '通道' in model_name and '门控' not in model_name:
            # 仅通道注意力
            channel_heatmap = np.random.beta(2, 5, (224, 224))
            axes[i, 1].imshow(image)
            axes[i, 1].imshow(channel_heatmap, cmap='jet', alpha=0.5)
            axes[i, 1].set_title('通道注意力热力图', fontsize=12, fontweight='bold')
            axes[i, 1].axis('off')
            
            # 隐藏其他子图
            for j in range(2, 4):
                axes[i, j].axis('off')
                
        elif '空间' in model_name and '门控' not in model_name:
            # 仅空间注意力
            spatial_heatmap = np.random.beta(2, 5, (224, 224))
            axes[i, 1].imshow(image)
            axes[i, 1].imshow(spatial_heatmap, cmap='jet', alpha=0.5)
            axes[i, 1].set_title('空间注意力热力图', fontsize=12, fontweight='bold')
            axes[i, 1].axis('off')
            
            # 隐藏其他子图
            for j in range(2, 4):
                axes[i, j].axis('off')
                
        else:  # 门控双注意力
            # 通道注意力
            channel_heatmap = np.random.beta(2, 5, (224, 224))
            axes[i, 1].imshow(image)
            axes[i, 1].imshow(channel_heatmap, cmap='jet', alpha=0.5)
            axes[i, 1].set_title('通道注意力', fontsize=12, fontweight='bold')
            axes[i, 1].axis('off')
            
            # 空间注意力
            spatial_heatmap = np.random.beta(2, 5, (224, 224))
            axes[i, 2].imshow(image)
            axes[i, 2].imshow(spatial_heatmap, cmap='jet', alpha=0.5)
            axes[i, 2].set_title('空间注意力', fontsize=12, fontweight='bold')
            axes[i, 2].axis('off')
            
            # 融合注意力
            fused_heatmap = (channel_heatmap + spatial_heatmap) / 2
            axes[i, 3].imshow(image)
            axes[i, 3].imshow(fused_heatmap, cmap='jet', alpha=0.5)
            axes[i, 3].set_title('门控融合注意力', fontsize=12, fontweight='bold')
            axes[i, 3].axis('off')
        
        # 添加模型名称
        axes[i, 0].text(-50, 112, model_name, rotation=90, fontsize=14, fontweight='bold',
                       ha='center', va='center', transform=axes[i, 0].transData)
    
    plt.suptitle('注意力机制热力图对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('注意力热力图对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 注意力热力图已生成: 注意力热力图对比.png")

def create_ablation_table(models_data):
    """创建中文消融实验对比表"""
    results = []
    
    model_mapping = {
        'ResNet-50基线模型': {'params': '23.9M', 'flops': '4.1G'},
        '仅通道注意力模型': {'params': '24.4M', 'flops': '4.2G'},
        '仅空间注意力模型': {'params': '23.9M', 'flops': '4.2G'},
        '简单双注意力模型': {'params': '24.4M', 'flops': '4.4G'},
        '门控双注意力模型': {'params': '32.8M', 'flops': '4.5G'}
    }
    
    for model_name, data in models_data.items():
        best_test_acc = np.max(data['test_acc'])
        final_test_acc = data['test_acc'][-1]
        
        results.append({
            '模型名称': model_name,
            '最佳测试准确率 (%)': f"{best_test_acc:.2f}",
            '最终测试准确率 (%)': f"{final_test_acc:.2f}",
            '参数量': model_mapping[model_name]['params'],
            '计算量 (FLOPs)': model_mapping[model_name]['flops'],
            '相对基线提升': f"+{best_test_acc - np.max(models_data['ResNet-50基线模型']['test_acc']):.2f}%" if model_name != 'ResNet-50基线模型' else "基线"
        })
    
    df = pd.DataFrame(results)
    
    # 创建可视化表格
    fig, ax = plt.subplots(figsize=(16, 8))
    ax.axis('tight')
    ax.axis('off')
    
    table = ax.table(cellText=df.values, colLabels=df.columns, 
                    cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(11)
    table.scale(1.2, 2.5)
    
    # 设置表格样式
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 设置行颜色
    colors = ['#ffebee', '#fff3e0', '#e8f5e8', '#e1f5fe', '#f3e5f5']
    for i in range(1, len(df) + 1):
        for j in range(len(df.columns)):
            table[(i, j)].set_facecolor(colors[i-1])
            if j == 0:  # 模型名称列加粗
                table[(i, j)].set_text_props(weight='bold')
    
    plt.title('消融实验结果对比表', fontsize=18, fontweight='bold', pad=20)
    plt.savefig('消融实验对比表.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存CSV文件
    df.to_csv('消融实验结果.csv', index=False, encoding='utf-8-sig')
    
    print("✓ 消融实验对比表已生成: 消融实验对比表.png")
    print("✓ 消融实验结果已保存: 消融实验结果.csv")
    
    return df

def create_architecture_diagram():
    """创建注意力模块连接示意图的代码"""
    diagram_code = """
graph TD
    A[输入图像<br/>224×224×3] --> B[ResNet-50骨干网络]
    B --> C[特征图<br/>7×7×2048]
    
    C --> D[通道注意力模块]
    C --> E[空间注意力模块]
    
    D --> F[全局平均池化<br/>7×7×2048 → 1×1×2048]
    F --> G[全连接层<br/>2048 → 128]
    G --> H[ReLU激活]
    H --> I[全连接层<br/>128 → 2048]
    I --> J[Sigmoid激活]
    J --> K[通道权重<br/>1×1×2048]
    
    E --> L[通道维度池化<br/>7×7×2048 → 7×7×2]
    L --> M[特征拼接<br/>7×7×2]
    M --> N[卷积层<br/>2→1, 核大小=7×7]
    N --> O[Sigmoid激活]
    O --> P[空间权重<br/>7×7×1]
    
    C --> Q[逐元素相乘]
    K --> Q
    C --> R[逐元素相乘]
    P --> R
    
    Q --> S[通道注意力特征<br/>7×7×2048]
    R --> T[空间注意力特征<br/>7×7×2048]
    
    S --> U[特征拼接<br/>7×7×4096]
    T --> U
    U --> V[门控网络<br/>Conv2d 4096→2048]
    V --> W[Sigmoid门控权重<br/>7×7×2048]
    
    S --> X[加权融合<br/>CA × Gate + SA × (1-Gate)]
    T --> X
    W --> X
    
    X --> Y[融合特征<br/>7×7×2048]
    Y --> Z[全局平均池化<br/>7×7×2048 → 1×1×2048]
    Z --> AA[展平<br/>2048]
    AA --> BB[Dropout 0.5]
    BB --> CC[全连接层<br/>2048 → 200]
    CC --> DD[输出预测<br/>200类鸟类]
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style DD fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style D fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style E fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style V fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style X fill:#ffebee,stroke:#c62828,stroke-width:2px
    style C fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style Y fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
"""
    
    # 保存架构图代码
    with open("注意力架构图.md", "w", encoding="utf-8") as f:
        f.write("# 双注意力融合ResNet架构图\n\n")
        f.write("```mermaid\n")
        f.write(diagram_code)
        f.write("\n```\n")
    
    print("✓ 注意力架构图代码已保存: 注意力架构图.md")

def main():
    """主函数：生成所有可视化结果"""
    print("=" * 60)
    print("开始生成5个模型的完整可视化结果")
    print("=" * 60)
    
    print("1. 生成训练精度对比图...")
    models_data = generate_training_accuracy_curves()
    
    print("\n2. 生成混淆矩阵对比图...")
    generate_confusion_matrices()
    
    print("\n3. 生成注意力热力图...")
    generate_attention_heatmaps()
    
    print("\n4. 生成消融实验对比表...")
    ablation_df = create_ablation_table(models_data)
    
    print("\n5. 生成注意力架构图...")
    create_architecture_diagram()
    
    print("\n" + "=" * 60)
    print("所有可视化结果生成完成！")
    print("=" * 60)
    print("生成的文件:")
    print("📊 五模型精度对比图.png - 训练和测试精度曲线")
    print("🎯 五模型混淆矩阵对比.png - 分类性能混淆矩阵")
    print("🔥 注意力热力图对比.png - 注意力机制可视化")
    print("📋 消融实验对比表.png - 实验结果对比表格")
    print("📄 消融实验结果.csv - 详细数据表格")
    print("🏗️ 注意力架构图.md - 模型架构流程图")
    print("=" * 60)
    
    # 显示消融实验结果
    print("\n消融实验结果预览:")
    print(ablation_df.to_string(index=False))

if __name__ == "__main__":
    main()
