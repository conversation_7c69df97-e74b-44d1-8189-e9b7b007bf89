import os
import shutil
from pathlib import Path

# 配置路径（根据实际路径调整）
CUB_ROOT = r"C:\Users\<USER>\Desktop\代码\CUB_200_2011"
IMAGES_DIR = os.path.join(CUB_ROOT, "images")
SPLIT_FILE = os.path.join(CUB_ROOT, "train_test_split.txt")
IMAGES_TXT = os.path.join(CUB_ROOT, "images.txt")
TARGET_DIR = os.path.join(CUB_ROOT, "processed")  # 输出目录（包含train/test）

def prepare_dataset():
    # 创建输出目录
    train_dir = os.path.join(TARGET_DIR, "train")
    test_dir = os.path.join(TARGET_DIR, "test")
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(test_dir, exist_ok=True)

    # 步骤1：读取train_test_split.txt，建立image_id到训练/测试的映射
    split_map = {}
    with open(SPLIT_FILE, "r") as f:
        for line in f:
            img_id, is_train = line.strip().split()
            split_map[img_id] = "train" if is_train == "1" else "test"

    # 步骤2：读取images.txt，建立image_id到图像路径的映射
    image_path_map = {}
    with open(IMAGES_TXT, "r") as f:
        for line in f:
            img_id, img_path = line.strip().split()
            image_path_map[img_id] = img_path  # 格式：class_id/image_name.jpg

    # 步骤3：复制图像到对应目录
    for img_id, img_rel_path in image_path_map.items():
        # 获取训练/测试标签
        split_type = split_map.get(img_id)
        if not split_type:
            print(f"警告：image_id {img_id} 未找到划分信息，跳过...")
            continue

        # 原始图像路径
        src_path = os.path.join(IMAGES_DIR, img_rel_path)
        if not os.path.exists(src_path):
            print(f"警告：图像文件 {src_path} 不存在，跳过...")
            continue

        # 目标路径（保持物种子目录结构）
        class_dir = img_rel_path.split("/")[0]  # 提取物种目录（如001.Black_footed_Albatross）
        target_path = os.path.join(TARGET_DIR, split_type, class_dir)
        os.makedirs(target_path, exist_ok=True)

        # 复制文件（避免重复复制）
        dest_path = os.path.join(target_path, os.path.basename(img_rel_path))
        if not os.path.exists(dest_path):
            shutil.copy(src_path, dest_path)

    print(f"数据划分完成！结果保存在：{TARGET_DIR}")

if __name__ == "__main__":
    prepare_dataset()