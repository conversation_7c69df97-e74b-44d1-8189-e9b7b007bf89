"""
可视化注意力模型架构的脚本
"""

def create_attention_architecture_diagram():
    """创建注意力模块连接示意图"""
    diagram_code = """
graph TD
    A[Input Image<br/>224×224×3] --> B[ResNet-50 Backbone]
    B --> C[Feature Maps<br/>7×7×2048]
    
    C --> D[Channel Attention Module]
    C --> E[Spatial Attention Module]
    
    D --> F[Global Average Pooling<br/>7×7×2048 → 1×1×2048]
    F --> G[FC Layer<br/>2048 → 128]
    G --> H[ReLU Activation]
    H --> I[FC Layer<br/>128 → 2048]
    I --> J[Sigmoid Activation]
    J --> K[Channel Weights<br/>1×1×2048]
    
    E --> L[Channel-wise<br/>Max & Avg Pool<br/>7×7×2048 → 7×7×2]
    L --> M[Concatenation<br/>7×7×2]
    M --> N[Conv2d<br/>2→1, kernel=7×7]
    N --> O[Sigmoid Activation]
    O --> P[Spatial Weights<br/>7×7×1]
    
    C --> Q[Element-wise Multiply]
    K --> Q
    C --> R[Element-wise Multiply]
    P --> R
    
    Q --> S[Channel Attended Features<br/>7×7×2048]
    R --> T[Spatial Attended Features<br/>7×7×2048]
    
    S --> U[Feature Concatenation<br/>7×7×4096]
    T --> U
    U --> V[Gate Network<br/>Conv2d 4096→2048]
    V --> W[Sigmoid Gate Weights<br/>7×7×2048]
    
    S --> X[Weighted Fusion<br/>CA × Gate + SA × (1-Gate)]
    T --> X
    W --> X
    
    X --> Y[Fused Features<br/>7×7×2048]
    Y --> Z[Global Average Pooling<br/>7×7×2048 → 1×1×2048]
    Z --> AA[Flatten<br/>2048]
    AA --> BB[Dropout 0.5]
    BB --> CC[FC Layer<br/>2048 → 200]
    CC --> DD[Output Predictions<br/>200 classes]
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style DD fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style D fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style E fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style V fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style X fill:#ffebee,stroke:#c62828,stroke-width:2px
    style C fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style Y fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
"""
    return diagram_code

def create_ablation_comparison_diagram():
    """创建消融实验对比图"""
    diagram_code = """
graph LR
    subgraph "Baseline Models"
        A1[ResNet-50 Baseline<br/>No Attention]
        A2[Channel Attention Only<br/>SE-like Module]
        A3[Spatial Attention Only<br/>CBAM-like Module]
    end
    
    subgraph "Fusion Strategies"
        B1[Simple Addition<br/>CA + SA / 2]
        B2[Gated Fusion<br/>CA × Gate + SA × (1-Gate)]
    end
    
    subgraph "Performance Comparison"
        C1[Accuracy Results<br/>Baseline < Single < Simple < Gated]
        C2[Feature Quality<br/>More Discriminative Features]
        C3[Attention Visualization<br/>Better Localization]
    end
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    B1 --> C1
    B2 --> C1
    
    B2 --> C2
    B2 --> C3
    
    style A1 fill:#ffcdd2
    style A2 fill:#fff3e0
    style A3 fill:#e8f5e8
    style B1 fill:#e1f5fe
    style B2 fill:#f3e5f5
    style C1 fill:#c8e6c9
    style C2 fill:#c8e6c9
    style C3 fill:#c8e6c9
"""
    return diagram_code

def save_diagrams():
    """保存所有图表到文件"""
    
    # 保存主架构图
    arch_diagram = create_attention_architecture_diagram()
    with open("attention_architecture.md", "w", encoding="utf-8") as f:
        f.write("# 双注意力融合ResNet架构图\n\n")
        f.write("## 完整架构流程\n\n")
        f.write("```mermaid\n")
        f.write(arch_diagram)
        f.write("\n```\n\n")
        
        f.write("## 架构说明\n\n")
        f.write("### 1. 输入处理\n")
        f.write("- 输入图像尺寸：224×224×3\n")
        f.write("- 经过ResNet-50骨干网络提取特征\n")
        f.write("- 得到7×7×2048的特征图\n\n")
        
        f.write("### 2. 双注意力机制\n")
        f.write("#### 通道注意力（Channel Attention）\n")
        f.write("- 全局平均池化压缩空间维度\n")
        f.write("- 两层全连接网络学习通道重要性\n")
        f.write("- 输出通道权重用于特征加权\n\n")
        
        f.write("#### 空间注意力（Spatial Attention）\n")
        f.write("- 通道维度的最大池化和平均池化\n")
        f.write("- 7×7卷积学习空间位置重要性\n")
        f.write("- 输出空间权重用于特征加权\n\n")
        
        f.write("### 3. 门控融合机制\n")
        f.write("- 将通道注意力和空间注意力特征拼接\n")
        f.write("- 门控网络学习自适应融合权重\n")
        f.write("- 动态平衡两种注意力的贡献\n\n")
        
        f.write("### 4. 分类输出\n")
        f.write("- 全局平均池化得到特征向量\n")
        f.write("- Dropout防止过拟合\n")
        f.write("- 全连接层输出200类鸟类预测\n")
    
    # 保存消融实验对比图
    ablation_diagram = create_ablation_comparison_diagram()
    with open("ablation_comparison.md", "w", encoding="utf-8") as f:
        f.write("# 消融实验对比分析\n\n")
        f.write("## 实验设计\n\n")
        f.write("```mermaid\n")
        f.write(ablation_diagram)
        f.write("\n```\n\n")
        
        f.write("## 实验组设置\n\n")
        f.write("### 1. 基线模型\n")
        f.write("- **ResNet-50 Baseline**: 标准ResNet-50，无注意力机制\n")
        f.write("- **Channel Attention Only**: 仅使用通道注意力（类似SE-Net）\n")
        f.write("- **Spatial Attention Only**: 仅使用空间注意力（类似CBAM）\n\n")
        
        f.write("### 2. 融合策略对比\n")
        f.write("- **Simple Addition**: 简单平均融合 (CA + SA) / 2\n")
        f.write("- **Gated Fusion**: 门控自适应融合 CA × Gate + SA × (1-Gate)\n\n")
        
        f.write("### 3. 评估指标\n")
        f.write("- **准确率**: Top-1分类准确率\n")
        f.write("- **特征质量**: 通过可视化评估特征判别性\n")
        f.write("- **注意力定位**: 热力图分析注意力区域合理性\n")
    
    print("架构图和消融实验对比图已保存到:")
    print("- attention_architecture.md")
    print("- ablation_comparison.md")

if __name__ == "__main__":
    save_diagrams()
