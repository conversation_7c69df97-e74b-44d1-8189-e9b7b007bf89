import torch
import torch.nn as nn
 

class BottleneckBlock(nn.Module):
    """手动实现ResNet-50的瓶颈残差块"""
    expansion = 4  # 输出通道数是中间层的4倍

    def __init__(self, in_channels, out_channels, stride=1):
        super().__init__()
        # 1x1 降维卷积
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        # 3x3 空间特征提取
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        # 1x1 升维卷积
        self.conv3 = nn.Conv2d(out_channels, out_channels * self.expansion, 
                              kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm2d(out_channels * self.expansion)
        # 短接路径（处理维度/步长不一致）
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels * self.expansion:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels * self.expansion, 
                         kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels * self.expansion)
            )
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        identity = x
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        x += self.shortcut(identity)
        return self.relu(x)

class ResNet50(nn.Module):
    """手动构建ResNet-50主体结构"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.in_channels = 64
        # 初始卷积层
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        # 残差块堆叠（对应ResNet-50的4个stage）
        self.layer1 = self._make_layer(BottleneckBlock, 64, 3, stride=1)
        self.layer2 = self._make_layer(BottleneckBlock, 128, 4, stride=2)
        self.layer3 = self._make_layer(BottleneckBlock, 256, 6, stride=2)
        self.layer4 = self._make_layer(BottleneckBlock, 512, 3, stride=2)
        # 全局平均池化+分类头
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * BottleneckBlock.expansion, num_classes)

    def _make_layer(self, block, out_channels, num_blocks, stride):
        strides = [stride] + [1] * (num_blocks - 1)
        layers = []
        for s in strides:
            layers.append(block(self.in_channels, out_channels, s))
            self.in_channels = out_channels * block.expansion
        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.maxpool(x)
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)
        return x


class ChannelAttention(nn.Module):
    """通道注意力模块（类似SE Block）"""
    def __init__(self, in_channels, ratio=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // ratio, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // ratio, in_channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)  # 通道加权

class SpatialAttention(nn.Module):
    """空间注意力模块（基于卷积）"""
    def __init__(self, kernel_size=7):
        super().__init__()
        assert kernel_size in (3, 7), "kernel size must be 3 or 7"
        padding = 3 if kernel_size == 7 else 1
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 沿通道维度做最大池化和平均池化
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        y = torch.cat([avg_out, max_out], dim=1)
        y = self.conv(y)
        return x * self.sigmoid(y)  # 空间加权


class AttentionFusionResNet(nn.Module):
    """融合双注意力的ResNet-50"""
    def __init__(self, num_classes=200):
        super().__init__()
        # 基础ResNet-50（去掉原分类头）
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()  # 移除原全连接层
        # 注意力模块
        self.channel_attn = ChannelAttention(2048)  # ResNet-50最后一层输出通道2048
        self.spatial_attn = SpatialAttention()
        # 门控融合层（动态调整注意力权重）
        self.gate = nn.Sequential(
            nn.Conv2d(2048 * 2, 2048, kernel_size=1),  # 拼接后通道数翻倍
            nn.Sigmoid()
        )
        # 分类头
        self.fc = nn.Linear(2048, num_classes)

    def forward(self, x):
        # 基础特征提取
        features = self.backbone(x)  # shape: [B, 2048, 7, 7]
        # 分别应用注意力
        ca_features = self.channel_attn(features)
        sa_features = self.spatial_attn(features)
        # 门控融合：ca * gate + sa * (1 - gate)
        concat = torch.cat([ca_features, sa_features], dim=1)
        gate_weights = self.gate(concat)
        fused_features = ca_features * gate_weights + sa_features * (1 - gate_weights)
        # 分类
        pooled = torch.flatten(self.backbone.avgpool(fused_features), 1)
        return self.fc(pooled)