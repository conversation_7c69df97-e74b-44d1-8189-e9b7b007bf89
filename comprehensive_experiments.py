import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import ImageFolder
import os
import time
import csv
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns
from tqdm import tqdm
import pandas as pd
from PIL import Image
import multiprocessing

# 导入模型定义
from merged_train import (
    ResNet50, ChannelAttention, SpatialAttention, 
    AttentionFusionResNet, prepare_dataset
)

# 配置
CUB_ROOT = r"C:\Users\<USER>\Desktop\代码\CUB_200_2011"
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
BATCH_SIZE = 32
EPOCHS = 50
LR = 0.001

# 数据变换
train_transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.RandomCrop(224),
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(10),
    transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),
    transforms.ToTensor(),
    transforms.RandomErasing(p=0.3, scale=(0.02, 0.33), ratio=(0.3, 3.3), value=0),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# 消融实验模型定义
class BaselineResNet(nn.Module):
    """基线ResNet-50（无注意力）"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
    
    def forward(self, x):
        return self.backbone(x)

class ChannelOnlyResNet(nn.Module):
    """仅通道注意力的ResNet-50"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()
        self.channel_attn = ChannelAttention(2048)
        self.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(2048, num_classes)
        )
    
    def forward(self, x):
        features = self.backbone(x, return_features=True)
        ca_features = self.channel_attn(features)
        pooled = torch.flatten(self.backbone.avgpool(ca_features), 1)
        return self.fc(pooled)

class SpatialOnlyResNet(nn.Module):
    """仅空间注意力的ResNet-50"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()
        self.spatial_attn = SpatialAttention()
        self.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(2048, num_classes)
        )
    
    def forward(self, x):
        features = self.backbone(x, return_features=True)
        sa_features = self.spatial_attn(features)
        pooled = torch.flatten(self.backbone.avgpool(sa_features), 1)
        return self.fc(pooled)

class SimpleAttentionFusionResNet(nn.Module):
    """简单融合双注意力的ResNet-50（无门控）"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()
        self.channel_attn = ChannelAttention(2048)
        self.spatial_attn = SpatialAttention()
        self.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(2048, num_classes)
        )
    
    def forward(self, x):
        features = self.backbone(x, return_features=True)
        ca_features = self.channel_attn(features)
        sa_features = self.spatial_attn(features)
        # 简单平均融合
        fused_features = (ca_features + sa_features) / 2
        pooled = torch.flatten(self.backbone.avgpool(fused_features), 1)
        return self.fc(pooled)

def train_model(model, train_loader, test_loader, model_name, epochs=EPOCHS):
    """训练模型并记录训练过程"""
    model = model.to(DEVICE)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LR)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)
    
    train_losses, train_accs = [], []
    test_losses, test_accs = [], []
    best_acc = 0.0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        correct = 0
        total = 0
        
        train_pbar = tqdm(train_loader, desc=f"{model_name} Epoch {epoch+1}/{epochs} Train", leave=False)
        for inputs, labels in train_pbar:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        train_acc = 100 * correct / total
        train_loss_avg = train_loss / len(train_loader)
        train_losses.append(train_loss_avg)
        train_accs.append(train_acc)
        
        # 测试阶段
        model.eval()
        test_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for inputs, labels in test_loader:
                inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                test_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        test_acc = 100 * correct / total
        test_loss_avg = test_loss / len(test_loader)
        test_losses.append(test_loss_avg)
        test_accs.append(test_acc)
        
        scheduler.step()
        
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), f"best_{model_name.lower().replace(' ', '_')}.pth")
        
        print(f"{model_name} Epoch {epoch+1}/{epochs} | "
              f"Train Loss: {train_loss_avg:.4f} Acc: {train_acc:.2f}% | "
              f"Test Loss: {test_loss_avg:.4f} Acc: {test_acc:.2f}%")
    
    return {
        'train_losses': train_losses,
        'train_accs': train_accs,
        'test_losses': test_losses,
        'test_accs': test_accs,
        'best_acc': best_acc
    }

def plot_training_curves(results_dict, save_path="training_curves.png"):
    """绘制训练曲线"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    colors = ['blue', 'red', 'green', 'orange']
    
    for i, (model_name, results) in enumerate(results_dict.items()):
        color = colors[i % len(colors)]
        epochs = range(1, len(results['train_losses']) + 1)
        
        # 训练损失
        ax1.plot(epochs, results['train_losses'], color=color, label=model_name, linewidth=2)
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 测试损失
        ax2.plot(epochs, results['test_losses'], color=color, label=model_name, linewidth=2)
        ax2.set_title('Test Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        # 训练准确率
        ax3.plot(epochs, results['train_accs'], color=color, label=model_name, linewidth=2)
        ax3.set_title('Training Accuracy')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Accuracy (%)')
        ax3.legend()
        ax3.grid(True)
        
        # 测试准确率
        ax4.plot(epochs, results['test_accs'], color=color, label=model_name, linewidth=2)
        ax4.set_title('Test Accuracy')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Accuracy (%)')
        ax4.legend()
        ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"训练曲线已保存到: {save_path}")

def generate_confusion_matrix(model, test_loader, class_names, model_name, save_path=None):
    """生成混淆矩阵"""
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for inputs, labels in tqdm(test_loader, desc=f"生成{model_name}混淆矩阵"):
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # 计算混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    
    # 绘制混淆矩阵（只显示前20个类别以便可视化）
    plt.figure(figsize=(12, 10))
    cm_subset = cm[:20, :20]  # 只显示前20个类别
    class_names_subset = class_names[:20] if len(class_names) >= 20 else class_names
    
    sns.heatmap(cm_subset, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names_subset, yticklabels=class_names_subset)
    plt.title(f'{model_name} - Confusion Matrix (Top 20 Classes)')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"{model_name}混淆矩阵已保存到: {save_path}")
    else:
        plt.show()
    
    # 计算准确率
    accuracy = np.sum(np.array(all_preds) == np.array(all_labels)) / len(all_labels)
    return accuracy * 100

def create_ablation_table(results_dict, save_path="ablation_results.csv"):
    """创建消融实验对比表"""
    data = []
    for model_name, results in results_dict.items():
        data.append({
            'Model': model_name,
            'Best Test Accuracy (%)': f"{results['best_acc']:.2f}",
            'Final Train Accuracy (%)': f"{results['train_accs'][-1]:.2f}",
            'Final Test Accuracy (%)': f"{results['test_accs'][-1]:.2f}",
            'Final Train Loss': f"{results['train_losses'][-1]:.4f}",
            'Final Test Loss': f"{results['test_losses'][-1]:.4f}"
        })
    
    df = pd.DataFrame(data)
    df.to_csv(save_path, index=False)
    print(f"消融实验结果表已保存到: {save_path}")
    print("\n消融实验结果:")
    print(df.to_string(index=False))
    return df

def generate_attention_heatmap(model, image_tensor, save_path, title="Attention Heatmap"):
    """生成注意力热力图"""
    model.eval()

    # 获取特征图
    with torch.no_grad():
        features = model.backbone(image_tensor, return_features=True)

        if hasattr(model, 'channel_attn') and hasattr(model, 'spatial_attn'):
            # 双注意力模型
            ca_features = model.channel_attn(features)
            sa_features = model.spatial_attn(features)

            # 计算注意力权重
            ca_weights = torch.mean(ca_features, dim=1, keepdim=True)
            sa_weights = torch.mean(sa_features, dim=1, keepdim=True)

            # 归一化到0-1
            ca_weights = (ca_weights - ca_weights.min()) / (ca_weights.max() - ca_weights.min())
            sa_weights = (sa_weights - sa_weights.min()) / (sa_weights.max() - sa_weights.min())

            # 转换为numpy
            ca_heatmap = ca_weights.squeeze().cpu().numpy()
            sa_heatmap = sa_weights.squeeze().cpu().numpy()

            # 上采样到原图尺寸
            from scipy.ndimage import zoom
            ca_heatmap = zoom(ca_heatmap, (224/ca_heatmap.shape[0], 224/ca_heatmap.shape[1]))
            sa_heatmap = zoom(sa_heatmap, (224/sa_heatmap.shape[0], 224/sa_heatmap.shape[1]))

        elif hasattr(model, 'channel_attn'):
            # 仅通道注意力
            ca_features = model.channel_attn(features)
            ca_weights = torch.mean(ca_features, dim=1, keepdim=True)
            ca_weights = (ca_weights - ca_weights.min()) / (ca_weights.max() - ca_weights.min())
            ca_heatmap = ca_weights.squeeze().cpu().numpy()
            from scipy.ndimage import zoom
            ca_heatmap = zoom(ca_heatmap, (224/ca_heatmap.shape[0], 224/ca_heatmap.shape[1]))
            sa_heatmap = None

        elif hasattr(model, 'spatial_attn'):
            # 仅空间注意力
            sa_features = model.spatial_attn(features)
            sa_weights = torch.mean(sa_features, dim=1, keepdim=True)
            sa_weights = (sa_weights - sa_weights.min()) / (sa_weights.max() - sa_weights.min())
            sa_heatmap = sa_weights.squeeze().cpu().numpy()
            from scipy.ndimage import zoom
            sa_heatmap = zoom(sa_heatmap, (224/sa_heatmap.shape[0], 224/sa_heatmap.shape[1]))
            ca_heatmap = None
        else:
            # 基线模型，使用特征图的平均值作为热力图
            avg_features = torch.mean(features, dim=1, keepdim=True)
            avg_features = (avg_features - avg_features.min()) / (avg_features.max() - avg_features.min())
            heatmap = avg_features.squeeze().cpu().numpy()
            from scipy.ndimage import zoom
            heatmap = zoom(heatmap, (224/heatmap.shape[0], 224/heatmap.shape[1]))
            ca_heatmap = sa_heatmap = None

    # 逆归一化原图
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image = image_tensor.squeeze().cpu().numpy().transpose(1, 2, 0)
    image = (image * std) + mean
    image = np.clip(image, 0, 1)

    # 绘制热力图
    if ca_heatmap is not None and sa_heatmap is not None:
        # 双注意力
        fig, axes = plt.subplots(1, 4, figsize=(20, 5))
        axes[0].imshow(image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        axes[1].imshow(image)
        axes[1].imshow(ca_heatmap, cmap='jet', alpha=0.5)
        axes[1].set_title('Channel Attention')
        axes[1].axis('off')

        axes[2].imshow(image)
        axes[2].imshow(sa_heatmap, cmap='jet', alpha=0.5)
        axes[2].set_title('Spatial Attention')
        axes[2].axis('off')

        # 融合热力图
        fused_heatmap = (ca_heatmap + sa_heatmap) / 2
        axes[3].imshow(image)
        axes[3].imshow(fused_heatmap, cmap='jet', alpha=0.5)
        axes[3].set_title('Fused Attention')
        axes[3].axis('off')

    elif ca_heatmap is not None:
        # 仅通道注意力
        fig, axes = plt.subplots(1, 2, figsize=(10, 5))
        axes[0].imshow(image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        axes[1].imshow(image)
        axes[1].imshow(ca_heatmap, cmap='jet', alpha=0.5)
        axes[1].set_title('Channel Attention')
        axes[1].axis('off')

    elif sa_heatmap is not None:
        # 仅空间注意力
        fig, axes = plt.subplots(1, 2, figsize=(10, 5))
        axes[0].imshow(image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        axes[1].imshow(image)
        axes[1].imshow(sa_heatmap, cmap='jet', alpha=0.5)
        axes[1].set_title('Spatial Attention')
        axes[1].axis('off')
    else:
        # 基线模型
        fig, axes = plt.subplots(1, 2, figsize=(10, 5))
        axes[0].imshow(image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        axes[1].imshow(image)
        axes[1].imshow(heatmap, cmap='jet', alpha=0.5)
        axes[1].set_title('Feature Activation')
        axes[1].axis('off')

    plt.suptitle(title)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"注意力热力图已保存到: {save_path}")

def create_attention_architecture_diagram():
    """创建注意力模块连接示意图"""
    diagram_code = """
    graph TD
        A[Input Image 224x224x3] --> B[ResNet-50 Backbone]
        B --> C[Feature Maps 7x7x2048]

        C --> D[Channel Attention Module]
        C --> E[Spatial Attention Module]

        D --> F[Global Average Pooling]
        F --> G[FC Layer 2048→128]
        G --> H[ReLU]
        H --> I[FC Layer 128→2048]
        I --> J[Sigmoid]
        J --> K[Channel Weights]

        E --> L[Channel-wise Max & Avg Pool]
        L --> M[Concat 2 channels]
        M --> N[Conv2d 2→1, kernel=7]
        N --> O[Sigmoid]
        O --> P[Spatial Weights]

        C --> Q[Element-wise Multiply]
        K --> Q
        C --> R[Element-wise Multiply]
        P --> R

        Q --> S[Channel Attended Features]
        R --> T[Spatial Attended Features]

        S --> U[Concatenation]
        T --> U
        U --> V[Gate Network Conv2d 4096→2048]
        V --> W[Sigmoid Gate Weights]

        S --> X[Weighted Fusion]
        T --> X
        W --> X

        X --> Y[Fused Features 7x7x2048]
        Y --> Z[Global Average Pooling]
        Z --> AA[Dropout 0.5]
        AA --> BB[FC Layer 2048→200]
        BB --> CC[Output Predictions]

        style A fill:#e1f5fe
        style CC fill:#c8e6c9
        style D fill:#fff3e0
        style E fill:#fff3e0
        style V fill:#f3e5f5
        style X fill:#ffebee
    """
    return diagram_code

def run_comprehensive_experiments():
    """运行完整的实验流程"""
    print("开始综合实验...")

    # 准备数据
    print("1. 准备数据集...")
    prepare_dataset()

    # 加载数据
    train_dataset = ImageFolder(
        root=os.path.join(CUB_ROOT, "processed", "train"),
        transform=train_transform
    )
    test_dataset = ImageFolder(
        root=os.path.join(CUB_ROOT, "processed", "test"),
        transform=test_transform
    )

    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4)

    class_names = train_dataset.classes

    # 定义四个模型进行消融实验
    models = {
        'Baseline ResNet-50': BaselineResNet(),
        'Channel Attention Only': ChannelOnlyResNet(),
        'Spatial Attention Only': SpatialOnlyResNet(),
        'Simple Dual Attention': SimpleAttentionFusionResNet(),
        'Gated Dual Attention': AttentionFusionResNet()
    }

    print("2. 开始训练模型...")
    results = {}

    for model_name, model in models.items():
        print(f"\n训练 {model_name}...")
        result = train_model(model, train_loader, test_loader, model_name, epochs=EPOCHS)
        results[model_name] = result

    print("\n3. 生成训练曲线...")
    plot_training_curves(results, "training_curves_comparison.png")

    print("\n4. 创建消融实验对比表...")
    ablation_df = create_ablation_table(results, "ablation_results.csv")

    print("\n5. 生成混淆矩阵...")
    # 加载最佳模型并生成混淆矩阵
    for model_name, model in models.items():
        try:
            model_path = f"best_{model_name.lower().replace(' ', '_').replace('-', '_')}.pth"
            if os.path.exists(model_path):
                model.load_state_dict(torch.load(model_path, map_location=DEVICE))
                model = model.to(DEVICE)

                cm_path = f"confusion_matrix_{model_name.lower().replace(' ', '_').replace('-', '_')}.png"
                accuracy = generate_confusion_matrix(model, test_loader, class_names, model_name, cm_path)
                print(f"{model_name} 最终测试准确率: {accuracy:.2f}%")
        except Exception as e:
            print(f"生成 {model_name} 混淆矩阵时出错: {e}")

    print("\n6. 生成注意力热力图...")
    # 选择一些测试样本生成热力图
    test_iter = iter(test_loader)
    sample_images, sample_labels = next(test_iter)

    for i, (model_name, model) in enumerate(models.items()):
        if i >= 3:  # 只为前3个模型生成热力图
            break
        try:
            model_path = f"best_{model_name.lower().replace(' ', '_').replace('-', '_')}.pth"
            if os.path.exists(model_path):
                model.load_state_dict(torch.load(model_path, map_location=DEVICE))
                model = model.to(DEVICE)

                # 为前5张图片生成热力图
                for j in range(min(5, len(sample_images))):
                    image_tensor = sample_images[j:j+1].to(DEVICE)
                    heatmap_path = f"heatmap_{model_name.lower().replace(' ', '_').replace('-', '_')}_sample_{j+1}.png"
                    generate_attention_heatmap(model, image_tensor, heatmap_path,
                                             f"{model_name} - Sample {j+1}")
        except Exception as e:
            print(f"生成 {model_name} 热力图时出错: {e}")

    print("\n7. 创建注意力架构图...")
    diagram_code = create_attention_architecture_diagram()

    # 保存架构图代码
    with open("attention_architecture.md", "w", encoding="utf-8") as f:
        f.write("# 双注意力模型架构图\n\n")
        f.write("```mermaid\n")
        f.write(diagram_code)
        f.write("\n```\n")

    print("架构图代码已保存到 attention_architecture.md")

    print("\n8. 实验总结...")
    print("=" * 60)
    print("实验完成！生成的文件包括:")
    print("- training_curves_comparison.png: 训练曲线对比图")
    print("- ablation_results.csv: 消融实验结果表")
    print("- confusion_matrix_*.png: 各模型的混淆矩阵")
    print("- heatmap_*.png: 注意力热力图")
    print("- attention_architecture.md: 注意力架构图")
    print("- best_*.pth: 各模型的最佳权重文件")
    print("=" * 60)

    return results, ablation_df

if __name__ == '__main__':
    multiprocessing.freeze_support()

    # 运行完整实验
    results, ablation_df = run_comprehensive_experiments()
