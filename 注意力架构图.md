# 双注意力融合ResNet架构图

```mermaid

graph TD
    A[输入图像<br/>224×224×3] --> B[ResNet-50骨干网络]
    B --> C[特征图<br/>7×7×2048]
    
    C --> D[通道注意力模块]
    C --> E[空间注意力模块]
    
    D --> F[全局平均池化<br/>7×7×2048 → 1×1×2048]
    F --> G[全连接层<br/>2048 → 128]
    G --> H[ReLU激活]
    H --> I[全连接层<br/>128 → 2048]
    I --> J[Sigmoid激活]
    J --> K[通道权重<br/>1×1×2048]
    
    E --> L[通道维度池化<br/>7×7×2048 → 7×7×2]
    L --> M[特征拼接<br/>7×7×2]
    M --> N[卷积层<br/>2→1, 核大小=7×7]
    N --> O[Sigmoid激活]
    O --> P[空间权重<br/>7×7×1]
    
    C --> Q[逐元素相乘]
    K --> Q
    C --> R[逐元素相乘]
    P --> R
    
    Q --> S[通道注意力特征<br/>7×7×2048]
    R --> T[空间注意力特征<br/>7×7×2048]
    
    S --> U[特征拼接<br/>7×7×4096]
    T --> U
    U --> V[门控网络<br/>Conv2d 4096→2048]
    V --> W[Sigmoid门控权重<br/>7×7×2048]
    
    S --> X[加权融合<br/>CA × Gate + SA × (1-Gate)]
    T --> X
    W --> X
    
    X --> Y[融合特征<br/>7×7×2048]
    Y --> Z[全局平均池化<br/>7×7×2048 → 1×1×2048]
    Z --> AA[展平<br/>2048]
    AA --> BB[Dropout 0.5]
    BB --> CC[全连接层<br/>2048 → 200]
    CC --> DD[输出预测<br/>200类鸟类]
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style DD fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style D fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style E fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style V fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style X fill:#ffebee,stroke:#c62828,stroke-width:2px
    style C fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style Y fill:#f1f8e9,stroke:#558b2f,stroke-width:2px

```
