import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import ImageFolder
import os
import shutil
import time
import csv
import multiprocessing  # 合并多进程模块

# ------------------------- 数据预处理部分（原data_preparation.py） -------------------------
# 配置路径（根据实际路径调整）
CUB_ROOT = r"C:\Users\<USER>\Desktop\代码\CUB_200_2011"
IMAGES_DIR = os.path.join(CUB_ROOT, "images")
SPLIT_FILE = os.path.join(CUB_ROOT, "train_test_split.txt")
IMAGES_TXT = os.path.join(CUB_ROOT, "images.txt")
TARGET_DIR = os.path.join(CUB_ROOT, "processed")  # 输出目录（包含train/test）

def prepare_dataset():
    # 创建输出目录
    train_dir = os.path.join(TARGET_DIR, "train")
    test_dir = os.path.join(TARGET_DIR, "test")
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(test_dir, exist_ok=True)

    # 步骤1：读取train_test_split.txt，建立image_id到训练/测试的映射
    split_map = {}
    with open(SPLIT_FILE, "r") as f:
        for line in f:
            img_id, is_train = line.strip().split()
            split_map[img_id] = "train" if is_train == "1" else "test"

    # 步骤2：读取images.txt，建立image_id到图像路径的映射
    image_path_map = {}
    with open(IMAGES_TXT, "r") as f:
        for line in f:
            img_id, img_path = line.strip().split()
            image_path_map[img_id] = img_path  # 格式：class_id/image_name.jpg

    # 步骤3：复制图像到对应目录
    for img_id, img_rel_path in image_path_map.items():
        # 获取训练/测试标签
        split_type = split_map.get(img_id)
        if not split_type:
            print(f"警告：image_id {img_id} 未找到划分信息，跳过...")
            continue

        # 原始图像路径
        src_path = os.path.join(IMAGES_DIR, img_rel_path)
        if not os.path.exists(src_path):
            print(f"警告：图像文件 {src_path} 不存在，跳过...")
            continue

        # 目标路径（保持物种子目录结构）
        class_dir = img_rel_path.split("/")[0]  # 提取物种目录（如001.Black_footed_Albatross）
        target_path = os.path.join(TARGET_DIR, split_type, class_dir)
        os.makedirs(target_path, exist_ok=True)

        # 复制文件（避免重复复制）
        dest_path = os.path.join(target_path, os.path.basename(img_rel_path))
        if not os.path.exists(dest_path):
            shutil.copy(src_path, dest_path)

    print(f"数据划分完成！结果保存在：{TARGET_DIR}")

# ------------------------- 模型定义部分（原model.py） -------------------------
class BottleneckBlock(nn.Module):
    """手动实现ResNet-50的瓶颈残差块"""
    expansion = 4  # 输出通道数是中间层的4倍

    def __init__(self, in_channels, out_channels, stride=1):
        super().__init__()
        # 1x1 降维卷积
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        # 3x3 空间特征提取
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        # 1x1 升维卷积
        self.conv3 = nn.Conv2d(out_channels, out_channels * self.expansion, 
                              kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm2d(out_channels * self.expansion)
        # 短接路径（处理维度/步长不一致）
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels * self.expansion:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels * self.expansion, 
                         kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels * self.expansion)
            )
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        identity = x
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        x += self.shortcut(identity)
        return self.relu(x)

class ResNet50(nn.Module):
    """手动构建ResNet-50主体结构"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.in_channels = 64
        # 初始卷积层
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        # 残差块堆叠（对应ResNet-50的4个stage）
        self.layer1 = self._make_layer(BottleneckBlock, 64, 3, stride=1)
        self.layer2 = self._make_layer(BottleneckBlock, 128, 4, stride=2)
        self.layer3 = self._make_layer(BottleneckBlock, 256, 6, stride=2)
        self.layer4 = self._make_layer(BottleneckBlock, 512, 3, stride=2)
        # 全局平均池化+分类头
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * BottleneckBlock.expansion, num_classes)

    def _make_layer(self, block, out_channels, num_blocks, stride):
        strides = [stride] + [1] * (num_blocks - 1)
        layers = []
        for s in strides:
            layers.append(block(self.in_channels, out_channels, s))
            self.in_channels = out_channels * block.expansion
        return nn.Sequential(*layers)

    def forward(self, x, return_features=False):  # 修改：添加return_features参数
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.maxpool(x)
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)  # 此时x是4维特征图（形状：[batch, 2048, 7, 7]）
        
        if return_features:  # 新增：返回未展平的特征图
            return x
        
        # 原分类头逻辑（仅在不返回特征时执行）
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)
        return x

class ChannelAttention(nn.Module):
    """通道注意力模块（类似SE Block）"""
    def __init__(self, in_channels, ratio=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // ratio, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // ratio, in_channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)  # 通道加权

class SpatialAttention(nn.Module):
    """空间注意力模块（基于卷积）"""
    def __init__(self, kernel_size=7):
        super().__init__()
        assert kernel_size in (3, 7), "kernel size must be 3 or 7"
        padding = 3 if kernel_size == 7 else 1
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 沿通道维度做最大池化和平均池化
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        y = torch.cat([avg_out, max_out], dim=1)
        y = self.conv(y)
        return x * self.sigmoid(y)  # 空间加权

# ------------------------- 消融实验模型定义 -------------------------
class BaselineResNet(nn.Module):
    """基线ResNet-50（无注意力机制）"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)

    def forward(self, x):
        return self.backbone(x)

class ChannelOnlyResNet(nn.Module):
    """仅通道注意力的ResNet-50"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()  # 移除原全连接层
        self.channel_attn = ChannelAttention(2048)
        self.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(2048, num_classes)
        )

    def forward(self, x):
        features = self.backbone(x, return_features=True)
        ca_features = self.channel_attn(features)
        pooled = torch.flatten(self.backbone.avgpool(ca_features), 1)
        return self.fc(pooled)

class SpatialOnlyResNet(nn.Module):
    """仅空间注意力的ResNet-50"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()  # 移除原全连接层
        self.spatial_attn = SpatialAttention()
        self.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(2048, num_classes)
        )

    def forward(self, x):
        features = self.backbone(x, return_features=True)
        sa_features = self.spatial_attn(features)
        pooled = torch.flatten(self.backbone.avgpool(sa_features), 1)
        return self.fc(pooled)

class SimpleAttentionFusionResNet(nn.Module):
    """简单融合双注意力的ResNet-50（无门控机制）"""
    def __init__(self, num_classes=200):
        super().__init__()
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()  # 移除原全连接层
        self.channel_attn = ChannelAttention(2048)
        self.spatial_attn = SpatialAttention()
        self.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(2048, num_classes)
        )

    def forward(self, x):
        features = self.backbone(x, return_features=True)
        ca_features = self.channel_attn(features)
        sa_features = self.spatial_attn(features)
        # 简单平均融合（无门控）
        fused_features = (ca_features + sa_features) / 2
        pooled = torch.flatten(self.backbone.avgpool(fused_features), 1)
        return self.fc(pooled)

class AttentionFusionResNet(nn.Module):
    """门控融合双注意力的ResNet-50"""
    def __init__(self, num_classes=200):
        super().__init__()
        # 基础ResNet-50（去掉原分类头）
        self.backbone = ResNet50(num_classes)
        self.backbone.fc = nn.Identity()  # 移除原全连接层
        # 注意力模块
        self.channel_attn = ChannelAttention(2048)  # ResNet-50最后一层输出通道2048
        self.spatial_attn = SpatialAttention()
        # 门控融合层（动态调整注意力权重）
        self.gate = nn.Sequential(
            nn.Conv2d(2048 * 2, 2048, kernel_size=1),  # 拼接后通道数翻倍
            nn.Sigmoid()
        )
        # 分类头（修改）
        self.fc = nn.Sequential(
        nn.Dropout(0.5),  # 新增Dropout层（失活率0.5）
        nn.Linear(2048, num_classes)
        )

    def forward(self, x):
        # 修改：调用backbone时指定return_features=True，获取4维特征图
        features = self.backbone(x, return_features=True)  # 现在features形状：[batch, 2048, 7, 7]

        # 应用通道注意力和空间注意力（输入要求4维，现在维度正确）
        ca_features = self.channel_attn(features)
        sa_features = self.spatial_attn(features)

        # 门控融合：ca * gate + sa * (1 - gate)
        concat = torch.cat([ca_features, sa_features], dim=1)
        gate_weights = self.gate(concat)
        fused_features = ca_features * gate_weights + sa_features * (1 - gate_weights)
        # 分类（重新应用全局平均池化和展平）
        pooled = torch.flatten(self.backbone.avgpool(fused_features), 1)
        return self.fc(pooled)

# ------------------------- 训练逻辑部分（原train.py） -------------------------
# 新增：导入进度条库
from tqdm import tqdm

# 超参数配置
BATCH_SIZE = 32
EPOCHS = 100  # 修改：从50→100
LR = 0.001
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 数据增强（细粒度分类需要更激进的增强）
# 数据增强（调整后降低强度）
train_transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.RandomCrop(224),
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(10),  # 原15°→10°，减少旋转幅度
    transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),  # 降低颜色抖动强度
    transforms.ToTensor(),
    transforms.RandomErasing(p=0.3, scale=(0.02, 0.33), ratio=(0.3, 3.3), value=0),  # 原p=0.5→0.3，减少擦除概率
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# ------------------------- 新增：热力图相关导入和函数 -------------------------
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns

def save_heatmap(image_tensor, heatmap, save_path, title="Grad-CAM Heatmap"):  
    """将热力图叠加到原始图像并保存"""
    # 逆归一化（恢复原始图像）
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image = image_tensor.squeeze().cpu().numpy().transpose(1, 2, 0)  # [C,H,W]→[H,W,C]
    image = (image * std) + mean  # 逆归一化
    image = np.clip(image, 0, 1)  # 限制在0-1范围

    # 绘制热力图
    plt.figure(figsize=(10, 5))
    plt.subplot(1, 2, 1)
    plt.imshow(image)
    plt.title("Original Image")
    plt.subplot(1, 2, 2)
    plt.imshow(image)
    plt.imshow(heatmap, cmap="jet", alpha=0.5)
    plt.title(title)
    plt.savefig(save_path, bbox_inches="tight")
    plt.close()

# ------------------------- 修改：测试阶段添加热力图生成 ------------------------- 
# ------------------------- 新增：关键点解析与重叠率计算 ------------------------- 
import pandas as pd  # 需安装pandas
from PIL import Image  # 需安装Pillow

def parse_part_annotations(part_locs_path):
    """解析CUB-200-2011的关键点标注文件"""
    df = pd.read_csv(part_locs_path, sep=" ", header=None,
                    names=["image_id", "part_id", "x", "y", "visible"])
    # 仅保留可见的关键点
    visible_parts = df[df["visible"] == 1]
    # 按image_id分组，存储坐标列表
    annotations = {}  # key: image_id, value: list of (x,y) tuples
    for img_id, group in visible_parts.groupby("image_id"):
        annotations[img_id] = list(zip(group["x"], group["y"]))
    return annotations

def resize_keypoints(original_size, keypoints, target_size=(224, 224)):
    """将原始图像的关键点坐标映射到预处理后的尺寸"""
    orig_w, orig_h = original_size
    target_w, target_h = target_size
    # 计算缩放比例（假设预处理时先Resize到256x256，再RandomCrop到224x224，此处简化为直接按比例缩放）
    scale_x = target_w / orig_w
    scale_y = target_h / orig_h
    return [(x * scale_x, y * scale_y) for (x, y) in keypoints]

def calculate_overlap(heatmap_mask, keypoints):
    """计算关键点与注意力区域的重叠率（落在掩码内的点比例）"""
    if not keypoints:
        return 0.0  # 无可见关键点
    overlap_count = 0
    for (x, y) in keypoints:
        # 注意：heatmap_mask是二值数组（1表示注意力区域），需转换为整数坐标
        x_int = int(round(x))
        y_int = int(round(y))
        if 0 <= x_int < heatmap_mask.shape[1] and 0 <= y_int < heatmap_mask.shape[0]:
            if heatmap_mask[y_int, x_int] == 1:
                overlap_count += 1
    return overlap_count / len(keypoints)

# ------------------------- 训练函数定义 -------------------------
def train_single_model(model, model_name, train_loader, test_loader, epochs=EPOCHS):
    """训练单个模型"""
    print(f"\n开始训练 {model_name}...")

    model = model.to(DEVICE)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LR)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)

    # 训练日志
    log_path = f"training_log_{model_name.lower().replace(' ', '_').replace('-', '_')}.csv"
    if not os.path.exists(log_path):
        with open(log_path, "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["Epoch", "Train Loss", "Train Acc", "Test Loss", "Test Acc", "Time"])

    best_acc = 0.0
    train_losses, train_accs = [], []
    test_losses, test_accs = [], []

    for epoch in range(epochs):
        start_time = time.time()

        # 训练阶段
        model.train()
        train_loss = 0.0
        correct = 0
        total = 0

        train_pbar = tqdm(train_loader, desc=f"{model_name} Epoch {epoch+1}/{epochs} Train", leave=False)
        for inputs, labels in train_pbar:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            train_pbar.set_postfix({"Loss": f"{train_loss / (total // BATCH_SIZE + 1):.4f}"})

        train_acc = 100 * correct / total
        train_loss_avg = train_loss / len(train_loader)
        train_losses.append(train_loss_avg)
        train_accs.append(train_acc)

        # 测试阶段
        model.eval()
        test_loss = 0.0
        correct = 0
        total = 0

        test_pbar = tqdm(test_loader, desc=f"{model_name} Epoch {epoch+1}/{epochs} Test", leave=False)
        with torch.no_grad():
            for inputs, labels in test_pbar:
                inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
                outputs = model(inputs)
                loss = criterion(outputs, labels)

                test_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

                test_pbar.set_postfix({"Loss": f"{test_loss / (total // BATCH_SIZE + 1):.4f}"})

        test_acc = 100 * correct / total
        test_loss_avg = test_loss / len(test_loader)
        test_losses.append(test_loss_avg)
        test_accs.append(test_acc)

        scheduler.step()

        # 记录日志
        epoch_time = time.time() - start_time
        with open(log_path, "a", newline="") as f:
            writer = csv.writer(f)
            writer.writerow([epoch+1, f"{train_loss_avg:.4f}", f"{train_acc:.2f}%",
                            f"{test_loss_avg:.4f}", f"{test_acc:.2f}%", f"{epoch_time:.2f}s"])

        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            model_save_path = f"best_{model_name.lower().replace(' ', '_').replace('-', '_')}.pth"
            torch.save(model.state_dict(), model_save_path)

        print(f"{model_name} Epoch {epoch+1}/{epochs} | "
              f"Train Loss: {train_loss_avg:.4f} Acc: {train_acc:.2f}% | "
              f"Test Loss: {test_loss_avg:.4f} Acc: {test_acc:.2f}% | "
              f"Time: {epoch_time:.2f}s")

    print(f"{model_name} 训练完成，最佳测试准确率：{best_acc:.2f}%")
    return {
        'train_losses': train_losses,
        'train_accs': train_accs,
        'test_losses': test_losses,
        'test_accs': test_accs,
        'best_acc': best_acc
    }

# ------------------------- 消融实验主函数 -------------------------
def run_ablation_experiments():
    """运行完整的消融实验"""
    print("=" * 60)
    print("开始消融实验：训练5个模型进行对比分析")
    print("=" * 60)

    # 预处理数据
    prepare_dataset()

    # 加载数据
    train_dataset = ImageFolder(
        root=os.path.join(CUB_ROOT, "processed", "train"),
        transform=train_transform
    )
    test_dataset = ImageFolder(
        root=os.path.join(CUB_ROOT, "processed", "test"),
        transform=test_transform
    )

    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4)

    # 定义5个模型进行消融实验
    models_config = [
        ("ResNet-50 Baseline", BaselineResNet()),
        ("Channel Attention Only", ChannelOnlyResNet()),
        ("Spatial Attention Only", SpatialOnlyResNet()),
        ("Simple Dual Attention", SimpleAttentionFusionResNet()),
        ("Gated Dual Attention", AttentionFusionResNet())
    ]

    # 存储所有模型的训练结果
    all_results = {}

    # 依次训练每个模型
    for model_name, model in models_config:
        result = train_single_model(model, model_name, train_loader, test_loader, epochs=EPOCHS)
        all_results[model_name] = result

        # 每个模型训练完后清理GPU内存
        del model
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        print(f"\n{model_name} 训练完成，GPU内存已清理\n")

    # 生成消融实验对比表
    print("\n" + "=" * 60)
    print("消融实验结果汇总")
    print("=" * 60)

    results_data = []
    for model_name, result in all_results.items():
        results_data.append({
            'Model': model_name,
            'Best Test Accuracy (%)': f"{result['best_acc']:.2f}",
            'Final Train Accuracy (%)': f"{result['train_accs'][-1]:.2f}",
            'Final Test Accuracy (%)': f"{result['test_accs'][-1]:.2f}",
            'Final Train Loss': f"{result['train_losses'][-1]:.4f}",
            'Final Test Loss': f"{result['test_losses'][-1]:.4f}"
        })

    # 保存结果到CSV
    import pandas as pd
    df = pd.DataFrame(results_data)
    df.to_csv('ablation_experiment_results.csv', index=False)

    print("消融实验结果:")
    print(df.to_string(index=False))
    print(f"\n详细结果已保存到: ablation_experiment_results.csv")

    return all_results

# ------------------------- 单模型训练（原有功能保留） -------------------------
def train_single_gated_model():
    """训练单个门控双注意力模型（原有功能）"""
    # 预处理数据（调用data_preparation的函数）
    prepare_dataset()

    # 加载数据（使用预处理后的路径）
    train_dataset = ImageFolder(
        root=os.path.join(CUB_ROOT, "processed", "train"),
        transform=train_transform
    )
    test_dataset = ImageFolder(
        root=os.path.join(CUB_ROOT, "processed", "test"),
        transform=test_transform
    )

    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4)

    # 初始化模型、损失函数、优化器
    model = AttentionFusionResNet().to(DEVICE)
    criterion = nn.CrossEntropyLoss()
    # 优化器和调度器（修改）
    optimizer = optim.Adam(model.parameters(), lr=LR)
    # 余弦退火调度（总epoch=100，最后5个epoch学习率降至1e-6）
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)

    # 训练日志记录（CSV）
    # 修改日志路径为绝对路径
    log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "training_log_gated_dual_attention.csv")
    if not os.path.exists(log_path):
        with open(log_path, "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["Epoch", "Train Loss", "Train Acc", "Test Loss", "Test Acc", "Time"])

    # 训练循环
    best_acc = 0.0
    for epoch in range(EPOCHS):
        start_time = time.time()

        # 训练阶段（添加进度条）
        model.train()
        train_loss = 0.0
        correct = 0
        total = 0
        # 使用tqdm显示训练进度条
        train_pbar = tqdm(train_loader, desc=f"Gated Dual Attention Epoch {epoch+1}/{EPOCHS} Train", leave=False)
        for inputs, labels in train_pbar:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            # 实时更新进度条显示的损失
            train_pbar.set_postfix({"Loss": f"{train_loss / (total // BATCH_SIZE + 1):.4f}"})

        train_acc = 100 * correct / total
        train_loss_avg = train_loss / len(train_loader)

        # 测试阶段（添加进度条）
        model.eval()
        test_loss = 0.0
        correct = 0
        total = 0
        # 使用tqdm显示测试进度条
        test_pbar = tqdm(test_loader, desc=f"Gated Dual Attention Epoch {epoch+1}/{EPOCHS} Test", leave=False)
        with torch.no_grad():
            for inputs, labels in test_pbar:
                inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
                outputs = model(inputs)
                loss = criterion(outputs, labels)

                test_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

                # 实时更新进度条显示的损失
                test_pbar.set_postfix({"Loss": f"{test_loss / (total // BATCH_SIZE + 1):.4f}"})

        test_acc = 100 * correct / total
        test_loss_avg = test_loss / len(test_loader)
        scheduler.step()

        # 记录日志
        epoch_time = time.time() - start_time
        with open(log_path, "a", newline="") as f:
            writer = csv.writer(f)
            writer.writerow([epoch+1, f"{train_loss_avg:.4f}", f"{train_acc:.2f}%",
                            f"{test_loss_avg:.4f}", f"{test_acc:.2f}%", f"{epoch_time:.2f}s"])

        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), "best_gated_dual_attention.pth")

        print(f"Gated Dual Attention Epoch {epoch+1}/{EPOCHS} | "
              f"Train Loss: {train_loss_avg:.4f} Acc: {train_acc:.2f}% | "
              f"Test Loss: {test_loss_avg:.4f} Acc: {test_acc:.2f}% | "
              f"Time: {epoch_time:.2f}s")

    print(f"门控双注意力模型训练完成，最佳测试准确率：{best_acc:.2f}%")

# 测试阶段（修改）
def test_tta(model, loader, device, tta_times=3):
    model.eval()
    total = 0
    correct = 0
    with torch.no_grad():
        for inputs, labels in loader:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = 0
            # TTA：对同一张图片进行多次增强
            for _ in range(tta_times):
                augmented_inputs = test_tta_transform(inputs)  # 定义tta_transform（如随机翻转）
                outputs += model(augmented_inputs)
            _, predicted = torch.max(outputs / tta_times, 1)  # 平均预测
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    return 100 * correct / total

# 定义TTA的transform（在代码顶部添加）
test_tta_transform = transforms.Compose([
    transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转
    transforms.RandomCrop(224, padding=10),  # 随机小范围裁剪
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# ------------------------- 主函数：选择训练模式 -------------------------
if __name__ == '__main__':
    multiprocessing.freeze_support()

    print("=" * 60)
    print("双注意力融合ResNet训练程序")
    print("=" * 60)
    print("请选择训练模式:")
    print("1. 消融实验 - 训练所有5个模型进行对比")
    print("2. 单模型训练 - 仅训练门控双注意力模型")
    print("3. 退出")
    print("=" * 60)

    while True:
        try:
            choice = input("请输入选择 (1/2/3): ").strip()

            if choice == '1':
                print("\n选择了消融实验模式")
                print("将依次训练以下5个模型:")
                print("1. ResNet-50 Baseline (无注意力)")
                print("2. Channel Attention Only (仅通道注意力)")
                print("3. Spatial Attention Only (仅空间注意力)")
                print("4. Simple Dual Attention (简单双注意力融合)")
                print("5. Gated Dual Attention (门控双注意力融合)")

                confirm = input("\n确认开始消融实验? (y/n): ").strip().lower()
                if confirm == 'y':
                    run_ablation_experiments()
                else:
                    print("已取消消融实验")
                break

            elif choice == '2':
                print("\n选择了单模型训练模式")
                print("将训练门控双注意力融合模型")

                confirm = input("\n确认开始训练? (y/n): ").strip().lower()
                if confirm == 'y':
                    train_single_gated_model()
                else:
                    print("已取消训练")
                break

            elif choice == '3':
                print("程序退出")
                break

            else:
                print("无效选择，请输入 1、2 或 3")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            break

