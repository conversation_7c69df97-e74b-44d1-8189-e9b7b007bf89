import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import ImageFolder
from model import AttentionFusionResNet
import time
import csv
import os  
import multiprocessing  # 新增：导入多进程模块

# 配置数据集根路径（根据用户实际路径调整）
CUB_ROOT = r"C:\Users\<USER>\Desktop\代码\CUB_200_2011"

# 超参数配置
BATCH_SIZE = 32
EPOCHS = 50
LR = 0.001
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 数据增强（细粒度分类需要更激进的增强）
train_transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.RandomCrop(224),
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(15),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# 加载数据（使用data_preparation.py处理后的路径）
train_dataset = ImageFolder(
    root=os.path.join(CUB_ROOT, "processed", "train"),
    transform=train_transform
)
test_dataset = ImageFolder(
    root=os.path.join(CUB_ROOT, "processed", "test"),
    transform=test_transform
)

train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4)

# 初始化模型、损失函数、优化器
model = AttentionFusionResNet().to(DEVICE)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=LR)
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.5)

# 训练日志记录（CSV）
log_path = "training_log.csv"

# 将主逻辑包裹在if __name__ == '__main__'中（关键修改）
if __name__ == '__main__':
    multiprocessing.freeze_support()  # Windows系统需要，避免多进程启动问题

    # 训练循环
    best_acc = 0.0
    for epoch in range(EPOCHS):
        start_time = time.time()
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        correct = 0
        total = 0
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        train_acc = 100 * correct / total
        train_loss_avg = train_loss / len(train_loader)
        
        # 测试阶段
        model.eval()
        test_loss = 0.0
        correct = 0
        total = 0
        with torch.no_grad():
            for inputs, labels in test_loader:
                inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                test_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        test_acc = 100 * correct / total
        test_loss_avg = test_loss / len(test_loader)
        scheduler.step()
        
        # 记录日志
        epoch_time = time.time() - start_time
        with open(log_path, "a", newline="") as f:
            writer = csv.writer(f)
            writer.writerow([epoch+1, f"{train_loss_avg:.4f}", f"{train_acc:.2f}%", 
                            f"{test_loss_avg:.4f}", f"{test_acc:.2f}%", f"{epoch_time:.2f}s"])
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), "best_model.pth")
        
        print(f"Epoch {epoch+1}/{EPOCHS} | "
              f"Train Loss: {train_loss_avg:.4f} Acc: {train_acc:.2f}% | "
              f"Test Loss: {test_loss_avg:.4f} Acc: {test_acc:.2f}% | "
              f"Time: {epoch_time:.2f}s")

    print(f"训练完成，最佳测试准确率：{best_acc:.2f}%")