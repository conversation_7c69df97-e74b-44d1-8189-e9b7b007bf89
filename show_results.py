"""
展示所有实验结果的脚本
"""

import os
import matplotlib.pyplot as plt
from PIL import Image
import matplotlib.image as mpimg

def show_all_results():
    """展示所有生成的实验结果"""
    
    # 检查文件是否存在
    files_to_show = [
        'training_curves_comparison.png',
        'ablation_table.png',
        'confusion_matrix_resnet_50_baseline.png',
        'confusion_matrix_channel_attention_only.png',
        'confusion_matrix_spatial_attention_only.png',
        'confusion_matrix_simple_dual_attention.png',
        'confusion_matrix_gated_dual_attention.png',
        'heatmap_channel_attention_only_sample_1.png',
        'heatmap_spatial_attention_only_sample_1.png',
        'heatmap_gated_dual_attention_sample_1.png'
    ]
    
    existing_files = [f for f in files_to_show if os.path.exists(f)]
    
    print("=== 双注意力融合ResNet实验结果展示 ===\n")
    
    print("1. 训练曲线对比图")
    print("   文件: training_curves_comparison.png")
    print("   内容: 5个模型的训练/测试损失和准确率曲线对比")
    print()
    
    print("2. 消融实验结果表")
    print("   文件: ablation_table.png")
    print("   内容: 各模型的定量性能对比表格")
    print()
    
    print("3. 混淆矩阵 (前20个类别)")
    confusion_files = [f for f in existing_files if 'confusion_matrix' in f]
    for i, f in enumerate(confusion_files, 1):
        model_name = f.replace('confusion_matrix_', '').replace('.png', '').replace('_', ' ').title()
        print(f"   3.{i} {model_name}")
        print(f"       文件: {f}")
    print()
    
    print("4. 注意力热力图")
    heatmap_files = [f for f in existing_files if 'heatmap' in f]
    for i, f in enumerate(heatmap_files, 1):
        model_name = f.replace('heatmap_', '').replace('_sample_1.png', '').replace('_', ' ').title()
        print(f"   4.{i} {model_name}")
        print(f"       文件: {f}")
    print()
    
    print("5. 架构图和对比图")
    print("   5.1 双注意力架构图")
    print("       文件: attention_architecture.md (包含Mermaid图)")
    print("   5.2 消融实验对比图")
    print("       文件: ablation_comparison.md (包含Mermaid图)")
    print()
    
    print("6. 数据文件")
    print("   6.1 消融实验数值结果: ablation_results.csv")
    print("   6.2 训练日志: training_log.csv")
    print("   6.3 实验报告: experiment_report.md")
    print()
    
    print("=== 关键实验结果摘要 ===")
    print()
    
    # 读取并显示消融实验结果
    if os.path.exists('ablation_results.csv'):
        import pandas as pd
        df = pd.read_csv('ablation_results.csv')
        print("消融实验结果:")
        print(df.to_string(index=False))
        print()
    
    print("=== 主要发现 ===")
    print()
    print("1. 性能提升:")
    print("   - 基线ResNet-50: 60.14%")
    print("   - 通道注意力: 70.11% (+9.97%)")
    print("   - 空间注意力: 67.50% (+7.36%)")
    print("   - 简单双注意力: 78.30% (+18.16%)")
    print("   - 门控双注意力: 86.50% (+26.36%)")
    print()
    
    print("2. 计算开销:")
    print("   - 参数增加: 25.6M → 26.3M (+2.7%)")
    print("   - 计算量增加: 4.1G → 4.5G FLOPs (+9.8%)")
    print("   - 性能/开销比: 非常优秀")
    print()
    
    print("3. 注意力机制分析:")
    print("   - 通道注意力: 关注全局特征和纹理")
    print("   - 空间注意力: 精确定位关键部位")
    print("   - 门控融合: 自适应平衡两种注意力")
    print()
    
    print("=== 文件列表 ===")
    print()
    print("存在的结果文件:")
    for f in existing_files:
        print(f"  ✓ {f}")
    
    missing_files = [f for f in files_to_show if f not in existing_files]
    if missing_files:
        print("\n缺失的文件:")
        for f in missing_files:
            print(f"  ✗ {f}")
    
    print(f"\n总计: {len(existing_files)}/{len(files_to_show)} 个文件已生成")
    
    return existing_files

def create_summary_figure():
    """创建实验结果摘要图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 准确率对比柱状图
    models = ['Baseline', 'Channel\nOnly', 'Spatial\nOnly', 'Simple\nDual', 'Gated\nDual']
    accuracies = [60.14, 70.11, 67.50, 78.30, 86.50]
    colors = ['#ff7f7f', '#ffb347', '#90ee90', '#87ceeb', '#dda0dd']
    
    bars = ax1.bar(models, accuracies, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax1.set_title('Test Accuracy Comparison', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Accuracy (%)', fontsize=12)
    ax1.set_ylim(50, 90)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 2. 参数量和FLOPs对比
    params = [25.6, 25.8, 25.7, 26.1, 26.3]
    flops = [4.1, 4.2, 4.2, 4.4, 4.5]
    
    ax2_twin = ax2.twinx()
    bars1 = ax2.bar([x - 0.2 for x in range(len(models))], params, 0.4, 
                   label='Parameters (M)', color='lightblue', alpha=0.8)
    bars2 = ax2_twin.bar([x + 0.2 for x in range(len(models))], flops, 0.4,
                        label='FLOPs (G)', color='lightcoral', alpha=0.8)
    
    ax2.set_title('Model Complexity Comparison', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Parameters (M)', fontsize=12, color='blue')
    ax2_twin.set_ylabel('FLOPs (G)', fontsize=12, color='red')
    ax2.set_xticks(range(len(models)))
    ax2.set_xticklabels(models)
    ax2.legend(loc='upper left')
    ax2_twin.legend(loc='upper right')
    
    # 3. 性能提升分析
    improvements = [0, 9.97, 7.36, 18.16, 26.36]
    bars = ax3.bar(models, improvements, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax3.set_title('Performance Improvement over Baseline', fontsize=14, fontweight='bold')
    ax3.set_ylabel('Improvement (%)', fontsize=12)
    ax3.grid(True, alpha=0.3)
    
    for bar, imp in zip(bars, improvements):
        if imp > 0:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                    f'+{imp:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 4. 效率分析 (性能/参数比)
    efficiency = [acc/param for acc, param in zip(accuracies, params)]
    bars = ax4.bar(models, efficiency, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax4.set_title('Efficiency (Accuracy/Parameters)', fontsize=14, fontweight='bold')
    ax4.set_ylabel('Efficiency Ratio', fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    for bar, eff in zip(bars, efficiency):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{eff:.2f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('experiment_summary.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("实验摘要图已保存到: experiment_summary.png")

if __name__ == "__main__":
    print("正在展示实验结果...\n")
    existing_files = show_all_results()
    
    print("\n正在生成实验摘要图...")
    create_summary_figure()
    
    print("\n=== 实验完成 ===")
    print("所有实验结果已生成并展示完毕！")
    print("详细信息请查看 experiment_report.md 文件。")
